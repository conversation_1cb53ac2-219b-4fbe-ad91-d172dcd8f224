"""
Langfuse integration for tracking and benchmarking LLM performance.
"""

import os
from typing import Dict, Any, List, Optional
from datetime import datetime
import json

try:
    from langfuse import Langfuse, observe
    LANGFUSE_AVAILABLE = True
except ImportError:
    LANGFUSE_AVAILABLE = False
    print("Warning: Langfuse not available. Install with: pip install langfuse")
    # Create dummy classes/functions for when Langfuse is not available

    class Langfuse:
        pass

    def observe(*args, **kwargs):
        """Dummy observe decorator when Langfuse is not available."""
        def decorator(func):
            return func
        if args and callable(args[0]):
            # Direct decoration without parentheses
            return decorator(args[0])
        return decorator

from ..models.loan_data import LoanData
from ..models.llm_response import LLMResponse, BatchEvaluationResult, ComparisonResult


class LangfuseTracker:
    """Langfuse integration for tracking loan approval predictions."""

    def __init__(self,
                 project_name: str = "loan-approval-prediction",
                 enabled: bool = True,
                 sample_rate: float = 1.0):
        """
        Initialize Langfuse tracker.

        Args:
            project_name: Name of the <PERSON>fuse project
            enabled: Whether tracking is enabled
            sample_rate: Sampling rate for traces (0.0 to 1.0)
        """
        self.enabled = enabled and LANGFUSE_AVAILABLE
        self.sample_rate = sample_rate
        self.project_name = project_name

        if self.enabled:
            try:
                self.langfuse = Langfuse(
                    secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
                    public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
                    host=os.getenv("LANGFUSE_HOST",
                                   "https://cloud.langfuse.com")
                )
                print(f"Langfuse tracking enabled for project: {project_name}")
            except Exception as e:
                print(f"Failed to initialize Langfuse: {e}")
                self.enabled = False
        else:
            self.langfuse = None
            if not LANGFUSE_AVAILABLE:
                print("Langfuse tracking disabled: package not installed")
            else:
                print("Langfuse tracking disabled")

    def track_single_prediction(self,
                                loan_data: LoanData,
                                prediction: LLMResponse,
                                approach_name: str,
                                actual_outcome: Optional[bool] = None,
                                session_id: Optional[str] = None) -> Optional[str]:
        """
        Track a single loan approval prediction.

        Args:
            loan_data: Input loan data
            prediction: LLM prediction response
            approach_name: Name of the approach used
            actual_outcome: Actual loan outcome (if available)
            session_id: Optional session identifier

        Returns:
            Trace ID if tracking is enabled, None otherwise
        """
        if not self.enabled or not self._should_sample():
            return None

        try:
            # Create trace
            trace = self.langfuse.trace(
                name=f"loan_approval_prediction",
                session_id=session_id,
                metadata={
                    "approach": approach_name,
                    "project": self.project_name,
                    "loan_amount": loan_data.loan_amnt,
                    "loan_grade": loan_data.grade.value,
                    "fico_avg": loan_data.fico_avg
                }
            )

            # Add input observation
            input_span = trace.span(
                name="loan_data_input",
                input={
                    "loan_amount": loan_data.loan_amnt,
                    "interest_rate": loan_data.int_rate,
                    "grade": loan_data.grade.value,
                    "annual_income": loan_data.annual_inc,
                    "dti": loan_data.dti,
                    "fico_range": f"{loan_data.fico_range_low}-{loan_data.fico_range_high}",
                    "purpose": loan_data.purpose.value,
                    "home_ownership": loan_data.home_ownership.value
                }
            )

            # Add prediction observation
            prediction_span = trace.span(
                name="llm_prediction",
                input=loan_data.to_prompt_context(),
                output={
                    "decision": prediction.decision.value,
                    "confidence": prediction.confidence.value,
                    "risk_assessment": prediction.risk_assessment.value,
                    "reasoning": prediction.reasoning,
                    "key_factors": prediction.key_factors,
                    "positive_factors": prediction.positive_factors,
                    "negative_factors": prediction.negative_factors,
                    "approval_probability": prediction.approval_probability,
                    "default_probability": prediction.default_probability
                },
                metadata={
                    "model_name": prediction.model_name,
                    "processing_time": prediction.processing_time,
                    "approach": approach_name
                }
            )

            # Add evaluation if actual outcome is available
            if actual_outcome is not None:
                is_correct = (prediction.decision.value ==
                              "APPROVE") == actual_outcome

                evaluation_span = trace.span(
                    name="prediction_evaluation",
                    input={
                        "predicted_decision": prediction.decision.value,
                        "actual_outcome": actual_outcome
                    },
                    output={
                        "is_correct": is_correct,
                        "prediction_type": self._get_prediction_type(prediction.decision.value == "APPROVE", actual_outcome)
                    },
                    metadata={
                        "confidence_level": prediction.confidence.value,
                        "risk_level": prediction.risk_assessment.value
                    }
                )

                # Add score for correctness
                trace.score(
                    name="prediction_accuracy",
                    value=1.0 if is_correct else 0.0,
                    comment=f"Prediction {'correct' if is_correct else 'incorrect'}"
                )

            return trace.id

        except Exception as e:
            print(f"Error tracking prediction with Langfuse: {e}")
            return None

    def track_batch_evaluation(self,
                               batch_result: BatchEvaluationResult,
                               session_id: Optional[str] = None) -> Optional[str]:
        """
        Track batch evaluation results.

        Args:
            batch_result: Batch evaluation result
            session_id: Optional session identifier

        Returns:
            Trace ID if tracking is enabled, None otherwise
        """
        if not self.enabled:
            return None

        try:
            # Create trace for batch evaluation
            trace = self.langfuse.trace(
                name="batch_evaluation",
                session_id=session_id,
                metadata={
                    "approach": batch_result.approach_name,
                    "total_predictions": batch_result.total_predictions,
                    "evaluation_duration": batch_result.evaluation_duration
                }
            )

            # Add batch metrics
            metrics_span = trace.span(
                name="batch_metrics",
                output={
                    "accuracy": batch_result.accuracy,
                    "precision": batch_result.precision,
                    "recall": batch_result.recall,
                    "f1_score": batch_result.f1_score,
                    "confusion_matrix": {
                        "true_positives": batch_result.true_positives,
                        "true_negatives": batch_result.true_negatives,
                        "false_positives": batch_result.false_positives,
                        "false_negatives": batch_result.false_negatives
                    }
                }
            )

            # Add scores for key metrics
            trace.score(name="accuracy", value=batch_result.accuracy)
            trace.score(name="precision", value=batch_result.precision)
            trace.score(name="recall", value=batch_result.recall)
            trace.score(name="f1_score", value=batch_result.f1_score)

            return trace.id

        except Exception as e:
            print(f"Error tracking batch evaluation with Langfuse: {e}")
            return None

    def track_approach_comparison(self,
                                  comparison_result: ComparisonResult,
                                  session_id: Optional[str] = None) -> Optional[str]:
        """
        Track comparison between different approaches.

        Args:
            comparison_result: Comparison result
            session_id: Optional session identifier

        Returns:
            Trace ID if tracking is enabled, None otherwise
        """
        if not self.enabled:
            return None

        try:
            # Create trace for comparison
            trace = self.langfuse.trace(
                name="approach_comparison",
                session_id=session_id,
                metadata={
                    "best_approach": comparison_result.best_approach,
                    "best_metric": comparison_result.best_metric,
                    "approaches_compared": list(comparison_result.approach_results.keys())
                }
            )

            # Add comparison results
            comparison_span = trace.span(
                name="comparison_results",
                output={
                    "performance_summary": comparison_result.get_performance_summary(),
                    "rankings": {
                        "accuracy": comparison_result.accuracy_ranking,
                        "precision": comparison_result.precision_ranking,
                        "recall": comparison_result.recall_ranking,
                        "f1_score": comparison_result.f1_ranking
                    }
                }
            )

            # Add individual approach results
            for approach_name, batch_result in comparison_result.approach_results.items():
                approach_span = trace.span(
                    name=f"approach_{approach_name}",
                    output={
                        "accuracy": batch_result.accuracy,
                        "precision": batch_result.precision,
                        "recall": batch_result.recall,
                        "f1_score": batch_result.f1_score
                    },
                    metadata={
                        "total_predictions": batch_result.total_predictions,
                        "evaluation_duration": batch_result.evaluation_duration
                    }
                )

            return trace.id

        except Exception as e:
            print(f"Error tracking comparison with Langfuse: {e}")
            return None

    def create_session(self, session_name: str, metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Create a new session for grouping related traces.

        Args:
            session_name: Name of the session
            metadata: Optional metadata for the session

        Returns:
            Session ID if tracking is enabled, None otherwise
        """
        if not self.enabled:
            return None

        try:
            session_id = f"{session_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Create session trace
            self.langfuse.trace(
                name="session_start",
                session_id=session_id,
                metadata={
                    "session_name": session_name,
                    "project": self.project_name,
                    **(metadata or {})
                }
            )

            return session_id

        except Exception as e:
            print(f"Error creating session with Langfuse: {e}")
            return None

    def add_feedback(self,
                     trace_id: str,
                     score: float,
                     comment: Optional[str] = None) -> bool:
        """
        Add feedback to a trace.

        Args:
            trace_id: ID of the trace to add feedback to
            score: Feedback score (0.0 to 1.0)
            comment: Optional comment

        Returns:
            True if feedback was added successfully, False otherwise
        """
        if not self.enabled:
            return False

        try:
            self.langfuse.score(
                trace_id=trace_id,
                name="user_feedback",
                value=score,
                comment=comment
            )
            return True

        except Exception as e:
            print(f"Error adding feedback with Langfuse: {e}")
            return False

    def get_analytics(self,
                      session_id: Optional[str] = None,
                      days: int = 7) -> Optional[Dict[str, Any]]:
        """
        Get analytics data from Langfuse.

        Args:
            session_id: Optional session ID to filter by
            days: Number of days to look back

        Returns:
            Analytics data if available, None otherwise
        """
        if not self.enabled:
            return None

        try:
            # This would require Langfuse API calls to get analytics
            # For now, return a placeholder
            return {
                "message": "Analytics retrieval not implemented yet",
                "session_id": session_id,
                "days": days
            }

        except Exception as e:
            print(f"Error getting analytics from Langfuse: {e}")
            return None

    def _should_sample(self) -> bool:
        """Determine if this trace should be sampled."""
        import random
        return random.random() < self.sample_rate

    def _get_prediction_type(self, predicted_approve: bool, actual_good: bool) -> str:
        """Get prediction type (TP, TN, FP, FN)."""
        if predicted_approve and actual_good:
            return "TP"
        elif not predicted_approve and not actual_good:
            return "TN"
        elif predicted_approve and not actual_good:
            return "FP"
        else:
            return "FN"

    def flush(self):
        """Flush any pending traces to Langfuse."""
        if self.enabled and self.langfuse:
            try:
                self.langfuse.flush()
            except Exception as e:
                print(f"Error flushing Langfuse traces: {e}")


# Decorator for automatic tracking
def track_prediction(tracker: LangfuseTracker, approach_name: str):
    """Decorator to automatically track predictions."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            if not tracker.enabled:
                return func(*args, **kwargs)

            # Extract loan_data from arguments
            loan_data = None
            for arg in args:
                if isinstance(arg, LoanData):
                    loan_data = arg
                    break

            if loan_data is None:
                # Look in kwargs
                loan_data = kwargs.get('loan_data')

            # Call the original function
            result = func(*args, **kwargs)

            # Track the prediction if we have the necessary data
            if loan_data and isinstance(result, LLMResponse):
                tracker.track_single_prediction(
                    loan_data=loan_data,
                    prediction=result,
                    approach_name=approach_name
                )

            return result

        return wrapper
    return decorator
