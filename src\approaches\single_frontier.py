"""
Single Frontier Model approach for loan approval prediction.
"""

import re
import time
from typing import Dict, Any, Optional
from datetime import datetime

from .base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>roach, PromptTemplate
from ..models.loan_data import LoanData
from ..models.llm_response import SingleModelResponse, LoanDecision, ConfidenceLevel, RiskLevel
from ..providers.base import LLMProvider


class SingleFrontierApproach(LoanApprovalApproach):
    """
    Single Frontier Model approach using one high-quality LLM for loan approval decisions.
    
    This approach uses a single, powerful language model (like GPT-4, Claude, or Gemini)
    to evaluate loan applications and make approval/denial decisions.
    """
    
    def __init__(self, provider: LLMProvider, name: str = "Single Frontier Model"):
        super().__init__(name)
        self.provider = provider
        self.prompt_template = PromptTemplate()
    
    def predict_single(self, loan_data: LoanData) -> SingleModelResponse:
        """
        Predict loan approval using a single frontier model.
        
        Args:
            loan_data: Loan application data
            
        Returns:
            SingleModelResponse with prediction and reasoning
        """
        start_time = time.time()
        
        try:
            # Generate the evaluation prompt
            prompt = self._create_evaluation_prompt(loan_data)
            
            # Get response from the LLM
            llm_response = self.provider.generate(prompt)
            
            if not llm_response.get("success", False):
                raise Exception(f"LLM generation failed: {llm_response.get('error', 'Unknown error')}")
            
            # Parse the response
            parsed_response = self._parse_llm_response(llm_response["response"])
            
            # Create structured response
            processing_time = time.time() - start_time
            
            response = SingleModelResponse(
                decision=parsed_response["decision"],
                confidence=parsed_response["confidence"],
                risk_assessment=parsed_response["risk_assessment"],
                reasoning=parsed_response["reasoning"],
                key_factors=parsed_response["key_factors"],
                positive_factors=parsed_response["positive_factors"],
                negative_factors=parsed_response["negative_factors"],
                approval_probability=parsed_response.get("approval_probability"),
                default_probability=parsed_response.get("default_probability"),
                model_name=self.provider.model_name,
                processing_time=processing_time,
                temperature_used=self.provider.config.temperature,
                max_tokens_used=self.provider.config.max_tokens
            )
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            # Return error response
            return SingleModelResponse(
                decision=LoanDecision.DENY,
                confidence=ConfidenceLevel.VERY_LOW,
                risk_assessment=RiskLevel.VERY_HIGH,
                reasoning=f"Error in loan evaluation: {str(e)}",
                key_factors=["System Error"],
                negative_factors=[f"Processing error: {str(e)}"],
                model_name=self.provider.model_name,
                processing_time=processing_time
            )
    
    def _create_evaluation_prompt(self, loan_data: LoanData) -> str:
        """Create the evaluation prompt for the LLM."""
        system_prompt = self.prompt_template.SYSTEM_PROMPT
        evaluation_prompt = self.prompt_template.format_loan_evaluation(loan_data)
        
        return f"{system_prompt}\n\n{evaluation_prompt}"
    
    def _parse_llm_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse the LLM response into structured data.
        
        Args:
            response_text: Raw response from the LLM
            
        Returns:
            Dictionary with parsed response components
        """
        parsed = {
            "decision": LoanDecision.DENY,  # Default to deny for safety
            "confidence": ConfidenceLevel.LOW,
            "risk_assessment": RiskLevel.HIGH,
            "reasoning": "",
            "key_factors": [],
            "positive_factors": [],
            "negative_factors": [],
            "approval_probability": None,
            "default_probability": None
        }
        
        try:
            # Extract decision
            decision_match = re.search(r'(?:DECISION|Decision):\s*\[?([A-Z_]+)\]?', response_text, re.IGNORECASE)
            if decision_match:
                decision_str = decision_match.group(1).upper()
                if decision_str in ["APPROVE", "APPROVED"]:
                    parsed["decision"] = LoanDecision.APPROVE
                elif decision_str in ["DENY", "DENIED", "REJECT", "REJECTED"]:
                    parsed["decision"] = LoanDecision.DENY
                elif decision_str in ["CONDITIONAL"]:
                    parsed["decision"] = LoanDecision.CONDITIONAL
            
            # Extract confidence
            confidence_match = re.search(r'(?:CONFIDENCE|Confidence):\s*\[?([A-Z_]+)\]?', response_text, re.IGNORECASE)
            if confidence_match:
                confidence_str = confidence_match.group(1).upper().replace(" ", "_")
                try:
                    parsed["confidence"] = ConfidenceLevel(confidence_str)
                except ValueError:
                    pass  # Keep default
            
            # Extract risk assessment
            risk_match = re.search(r'(?:RISK_ASSESSMENT|Risk Assessment):\s*\[?([A-Z_]+)\]?', response_text, re.IGNORECASE)
            if risk_match:
                risk_str = risk_match.group(1).upper().replace(" ", "_")
                try:
                    parsed["risk_assessment"] = RiskLevel(risk_str)
                except ValueError:
                    pass  # Keep default
            
            # Extract reasoning
            reasoning_match = re.search(r'(?:REASONING|Reasoning):\s*(.*?)(?=\n\d+\.|$)', response_text, re.IGNORECASE | re.DOTALL)
            if reasoning_match:
                parsed["reasoning"] = reasoning_match.group(1).strip()
            else:
                # Fallback: use the entire response as reasoning
                parsed["reasoning"] = response_text.strip()
            
            # Extract key factors
            key_factors_match = re.search(r'(?:KEY_FACTORS|Key Factors):\s*(.*?)(?=\n\d+\.|$)', response_text, re.IGNORECASE | re.DOTALL)
            if key_factors_match:
                factors_text = key_factors_match.group(1)
                parsed["key_factors"] = self._extract_list_items(factors_text)
            
            # Extract positive factors
            positive_match = re.search(r'(?:POSITIVE_FACTORS|Positive Factors):\s*(.*?)(?=\n\d+\.|$)', response_text, re.IGNORECASE | re.DOTALL)
            if positive_match:
                positive_text = positive_match.group(1)
                parsed["positive_factors"] = self._extract_list_items(positive_text)
            
            # Extract negative factors
            negative_match = re.search(r'(?:NEGATIVE_FACTORS|Negative Factors):\s*(.*?)(?=\n\d+\.|$)', response_text, re.IGNORECASE | re.DOTALL)
            if negative_match:
                negative_text = negative_match.group(1)
                parsed["negative_factors"] = self._extract_list_items(negative_text)
            
            # Extract probabilities
            approval_prob_match = re.search(r'(?:APPROVAL_PROBABILITY|Approval Probability):\s*([0-9.]+)', response_text, re.IGNORECASE)
            if approval_prob_match:
                try:
                    parsed["approval_probability"] = float(approval_prob_match.group(1))
                except ValueError:
                    pass
            
            default_prob_match = re.search(r'(?:DEFAULT_PROBABILITY|Default Probability):\s*([0-9.]+)', response_text, re.IGNORECASE)
            if default_prob_match:
                try:
                    parsed["default_probability"] = float(default_prob_match.group(1))
                except ValueError:
                    pass
            
        except Exception as e:
            # If parsing fails, at least include the raw response in reasoning
            parsed["reasoning"] = f"Failed to parse response: {str(e)}\n\nRaw response:\n{response_text}"
        
        return parsed
    
    def _extract_list_items(self, text: str) -> list:
        """Extract list items from text (handles bullet points, numbers, etc.)."""
        items = []
        
        # Split by lines and clean up
        lines = text.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Remove common list markers
            line = re.sub(r'^[-*•]\s*', '', line)  # Bullet points
            line = re.sub(r'^\d+\.\s*', '', line)  # Numbered lists
            line = re.sub(r'^[a-zA-Z]\.\s*', '', line)  # Letter lists
            
            if line:
                items.append(line)
        
        # If no items found, try comma-separated
        if not items and ',' in text:
            items = [item.strip() for item in text.split(',') if item.strip()]
        
        return items[:10]  # Limit to 10 items
    
    def get_approach_info(self) -> Dict[str, Any]:
        """Get information about this approach."""
        info = super().get_approach_info()
        info.update({
            "provider": self.provider.provider_name,
            "model_name": self.provider.model_name,
            "model_info": self.provider.get_model_info()
        })
        return info
